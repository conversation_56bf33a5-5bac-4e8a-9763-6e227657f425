PODS:
  - audioplayers_darwin (0.0.1):
    - Flutter
  - battery_plus (1.0.0):
    - Flutter
  - bi_flutter_background_location (0.0.1):
    - Flutter
  - bi_flutter_bluetooth_platform_api (1.0.0):
    - Flutter
    - iOSMcuManagerLibrary (= 1.6)
  - bi_flutter_notifications (0.0.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - CoreBluetoothMock (0.18.0)
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/AnalyticsWithoutAdIdSupport (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics/WithoutAdIdSupport (~> 11.10.0)
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Crashlytics (11.10.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.10.0)
  - Firebase/Messaging (11.10.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.10.0)
  - firebase_analytics (11.4.6):
    - Firebase/AnalyticsWithoutAdIdSupport (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_core (3.13.1):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_crashlytics (4.3.6):
    - Firebase/Crashlytics (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.2.6):
    - Firebase/Messaging (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics/WithoutAdIdSupport (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfigInterop (11.13.0)
  - FirebaseSessions (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_background_service_ios (0.0.3):
    - Flutter
  - flutter_blue_plus_darwin (0.0.2):
    - Flutter
    - FlutterMacOS
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - google_mlkit_commons (0.11.0):
    - Flutter
    - MLKitVision
  - google_mlkit_face_detection (0.13.1):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/FaceDetection (~> 7.0.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.10.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMLKit/FaceDetection (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitFaceDetection (~> 6.0.0)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.5.0)
  - integration_test (0.0.1):
    - Flutter
  - iOSMcuManagerLibrary (1.6):
    - SwiftCBOR (= 0.4.4)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - maps_launcher (0.0.1):
    - Flutter
  - mcumgr_flutter (0.0.2):
    - Flutter
    - iOSMcuManagerLibrary (= 1.6)
    - SwiftProtobuf
  - MLImage (1.0.0-beta6)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitFaceDetection (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - open_filex (0.0.2):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PhoneNumberKit/PhoneNumberKitCore (3.6.6)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SwiftCBOR (0.4.4)
  - SwiftProtobuf (1.29.0)
  - twilio_programmable_video (1.1.3):
    - Flutter
    - TwilioVideo (~> 4.6)
  - twilio_voice (0.0.1):
    - Flutter
    - TwilioVoice (~> 6.9.1)
  - TwilioVideo (4.6.3)
  - TwilioVoice (6.9.2)
  - url_launcher_ios (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - workmanager (0.0.1):
    - Flutter

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - battery_plus (from `.symlinks/plugins/battery_plus/ios`)
  - bi_flutter_background_location (from `.symlinks/plugins/bi_flutter_background_location/ios`)
  - bi_flutter_bluetooth_platform_api (from `.symlinks/plugins/bi_flutter_bluetooth_platform_api/ios`)
  - bi_flutter_notifications (from `.symlinks/plugins/bi_flutter_notifications/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - CoreBluetoothMock (~> 0.18)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Firebase/Crashlytics
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_background_service_ios (from `.symlinks/plugins/flutter_background_service_ios/ios`)
  - flutter_blue_plus_darwin (from `.symlinks/plugins/flutter_blue_plus_darwin/darwin`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - google_mlkit_commons (from `.symlinks/plugins/google_mlkit_commons/ios`)
  - google_mlkit_face_detection (from `.symlinks/plugins/google_mlkit_face_detection/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - maps_launcher (from `.symlinks/plugins/maps_launcher/ios`)
  - mcumgr_flutter (from `.symlinks/plugins/mcumgr_flutter/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - PhoneNumberKit/PhoneNumberKitCore (from `https://github.com/marmelroy/PhoneNumberKit`, tag `3.6.6`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - twilio_programmable_video (from `.symlinks/plugins/twilio_programmable_video/ios`)
  - twilio_voice (from `.symlinks/plugins/twilio_voice/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - workmanager (from `.symlinks/plugins/workmanager/ios`)

SPEC REPOS:
  trunk:
    - CoreBluetoothMock
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GTMSessionFetcher
    - iOSMcuManagerLibrary
    - libwebp
    - Mantle
    - MLImage
    - MLKitCommon
    - MLKitFaceDetection
    - MLKitVision
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftCBOR
    - SwiftProtobuf
    - TwilioVideo
    - TwilioVoice

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  battery_plus:
    :path: ".symlinks/plugins/battery_plus/ios"
  bi_flutter_background_location:
    :path: ".symlinks/plugins/bi_flutter_background_location/ios"
  bi_flutter_bluetooth_platform_api:
    :path: ".symlinks/plugins/bi_flutter_bluetooth_platform_api/ios"
  bi_flutter_notifications:
    :path: ".symlinks/plugins/bi_flutter_notifications/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_background_service_ios:
    :path: ".symlinks/plugins/flutter_background_service_ios/ios"
  flutter_blue_plus_darwin:
    :path: ".symlinks/plugins/flutter_blue_plus_darwin/darwin"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  google_mlkit_commons:
    :path: ".symlinks/plugins/google_mlkit_commons/ios"
  google_mlkit_face_detection:
    :path: ".symlinks/plugins/google_mlkit_face_detection/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  maps_launcher:
    :path: ".symlinks/plugins/maps_launcher/ios"
  mcumgr_flutter:
    :path: ".symlinks/plugins/mcumgr_flutter/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  PhoneNumberKit:
    :git: https://github.com/marmelroy/PhoneNumberKit
    :tag: 3.6.6
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  twilio_programmable_video:
    :path: ".symlinks/plugins/twilio_programmable_video/ios"
  twilio_voice:
    :path: ".symlinks/plugins/twilio_voice/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  workmanager:
    :path: ".symlinks/plugins/workmanager/ios"

CHECKOUT OPTIONS:
  PhoneNumberKit:
    :git: https://github.com/marmelroy/PhoneNumberKit
    :tag: 3.6.6

SPEC CHECKSUMS:
  audioplayers_darwin: ccf9c770ee768abb07e26d90af093f7bab1c12ab
  battery_plus: b42253f6d2dde71712f8c36fef456d99121c5977
  bi_flutter_background_location: ccdf46a2100185cada6f396d1980eb1031053299
  bi_flutter_bluetooth_platform_api: f10e54d046ef3738bf61ad94e70bea61402fd73e
  bi_flutter_notifications: dc08ad25bc798c979f4a9d8c1578492f3e49ee1a
  camera_avfoundation: be3be85408cd4126f250386828e9b1dfa40ab436
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  CoreBluetoothMock: 9de570a29520ea4b1201b8d23a228fb950d12b61
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_analytics: 2ec9a61c420dfdc4a740657faacae366d74e3410
  firebase_core: ba71b44041571da878cb624ce0d80250bcbe58ad
  firebase_crashlytics: 9301f26be1edba849e9a3a00b1d817ca1ae012b5
  firebase_messaging: 13129fe2ca166d1ed2d095062d76cee88943d067
  FirebaseAnalytics: 4e42333f02cf78ed93703a5c36f36dd518aebdef
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreExtension: 6f357679327f3614e995dc7cf3f2d600bdc774ac
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseCrashlytics: 84b073c997235740e6a951b7ee49608932877e5c
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseMessaging: 2b9f56aa4ed286e1f0ce2ee1d413aabb8f9f5cb9
  FirebaseRemoteConfigInterop: 7915cec47731a806cda541f90898ad0fab8f9f86
  FirebaseSessions: 9b3b30947b97a15370e0902ee7a90f50ef60ead6
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_background_service_ios: 00d31bdff7b4bfe06d32375df358abe0329cf87e
  flutter_blue_plus_darwin: 20a08bfeaa0f7804d524858d3d8744bcc1b6dbc3
  flutter_image_compress_common: 1697a328fd72bfb335507c6bca1a65fa5ad87df1
  flutter_local_notifications: a5a732f069baa862e728d839dd2ebb904737effb
  flutter_native_splash: 6cad9122ea0fad137d23137dd14b937f3e90b145
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  google_mlkit_commons: 2abe6a70e1824e431d16a51085cb475b672c8aab
  google_mlkit_face_detection: 754da2113a1952f063c7c5dc347ac6ae8934fb77
  GoogleAppMeasurement: 36684bfb3ee034e2b42b4321eb19da3a1b81e65d
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  integration_test: 4a889634ef21a45d28d50d622cf412dc6d9f586e
  iOSMcuManagerLibrary: 4102a4595be1c69e5286d7f1520a733c82c30b0a
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  maps_launcher: edf829809ba9e894d70e569bab11c16352dedb45
  mcumgr_flutter: 8c4a598cb1b4d10a9adbc0f13288334297185506
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitFaceDetection: 2a593db4837db503ad3426b565e7aab045cefea5
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  open_filex: 432f3cd11432da3e39f47fcc0df2b1603854eff1
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PhoneNumberKit: 54a4e3f3c776b3277b084acf00e2da18ff548d58
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  SwiftCBOR: ce5354ec8b660da2d6fc754462881119dbe1f963
  SwiftProtobuf: b7aa08087e2ab6d162862d143020091254095f69
  twilio_programmable_video: 63f74f0afa307a26321599cbbd965bd57c6134c4
  twilio_voice: 83eb5e1da5e6293d6c7609d7e9bfe7983082e05a
  TwilioVideo: 4afaac028c5886f2f3b313a9fcc146a6b7ca34e2
  TwilioVoice: e129d0d9533152e61b7d450db92ccc2159f08161
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  workmanager: 01be2de7f184bd15de93a1812936a2b7f42ef07e

PODFILE CHECKSUM: c690ade220ff813d23e480328d8432e3eb3b612b

COCOAPODS: 1.16.2
