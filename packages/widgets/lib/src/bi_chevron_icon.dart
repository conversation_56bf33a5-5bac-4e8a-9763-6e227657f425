import 'package:bi_flutter_font_awesome_pro/font_awesome_flutter.dart';
import 'package:flutter/material.dart';

enum BiIconFontType { regular, sharp, solid }

class BiChevronIcon {
  static IconData ofType(BuildContext context, BiIconFontType type) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    switch (type) {
      case BiIconFontType.regular:
        return isRTL ? Icons.chevron_left : Icons.chevron_right;
      case BiIconFontType.sharp:
        return isRTL ? Icons.chevron_left_sharp : Icons.chevron_right_sharp;
      case BiIconFontType.solid:
        return isRTL
            ? FontAwesomeIcons.solidChevronLeft
            : FontAwesomeIcons.solidChevronRight;
    }
  }
}
